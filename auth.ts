import NextAuth from 'next-auth';
import { authConfig } from './auth.config';
import Credentials from 'next-auth/providers/credentials';
import { loginAPI } from '@/networks/auth/login';
import { AuthLoginBodyI } from '@/networks/auth/types';

export const {
  handlers: { GET, POST },
  auth,
  signIn,
  signOut,
} = NextAuth({
  ...authConfig,
  providers: [
    ...authConfig.providers,
    Credentials({
      name: 'credentials',
      credentials: {
        type: { label: 'Type', type: 'text' },
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
        externalToken: { label: 'External Token', type: 'text' },
      },
      async authorize(credentials) {
        try {
          if (!credentials) return null;

          const { type, email, password, externalToken } = credentials as {
            type: string;
            email?: string;
            password?: string;
            externalToken?: string;
          };

          console.log('Credentials received:', {
            type,
            email: !!email,
            password: !!password,
            externalToken: !!externalToken,
          });

          const loginResponse = await loginAPI({
            type: type as AuthLoginBodyI['type'],
            email,
            password,
            externalToken,
          });

          console.log('Login API response:', {
            success: !!loginResponse?.token,
            profileId: loginResponse?.profileId,
          });

          if (loginResponse?.token) {
            return {
              id: loginResponse.profileId,
              email: loginResponse.email,
              name: loginResponse.name,
              image: loginResponse.avatar,
              backendData: loginResponse,
            };
          }

          return null;
        } catch (error) {
          console.error('Authorization Error:', error);
          return null;
        }
      },
    }),
  ],
  callbacks: {
    async signIn({ user, account }) {
      if (account?.provider === 'google') {
        try {
          console.log('Google sign-in callback triggered');
          // Call your backend API with the Google access token
          const loginResponse = await loginAPI({
            type: 'GOOGLE',
            externalToken: account.access_token!,
          });

          console.log('Google backend API response:', {
            success: !!loginResponse?.token,
          });

          if (loginResponse?.token) {
            // Store the backend response in the user object for the JWT callback
            (user as any).backendData = loginResponse;
            return true;
          }
          return false;
        } catch (error) {
          console.error('Google OAuth Backend Error:', error);
          return false;
        }
      }
      return true;
    },
    async jwt({ token, user }) {
      if (user) {
        console.log('JWT callback - user data:', {
          id: user.id,
          email: user.email,
          hasBackendData: !!(user as any).backendData,
        });

        // Get backend data from either Google OAuth or credentials
        const backendData = (user as any).backendData;

        if (backendData) {
          // Use backend data for token
          token.id = backendData.profileId;
          token.name = backendData.name;
          token.email = backendData.email;
          token.picture = backendData.avatar;
          token.accessToken = backendData.token;
          token.jwtToken = backendData.jwtToken;
          token.isUsernameSaved = backendData.isUsernameSaved;
          token.isPersonalDetailsSaved = backendData.isPersonalDetailsSaved;
          token.isWorkDetailsSaved = backendData.isWorkDetailsSaved;
          token.isPrivacyPolicyAccepted = backendData.isPrivacyPolicyAccepted;
        } else {
          // Fallback to user data (shouldn't happen with our setup)
          token.id = user.id;
          token.name = user.name;
          token.email = user.email;
          token.picture = user.image;
        }
      }
      return token;
    },
    async session({ session, token }) {
      // Pass the data from the JWT to the client-side session
      if (token) {
        session.user.id = token.id as string;
        session.user.name = token.name as string;
        session.user.email = token.email as string;
        session.user.image = token.picture as string;
        // Expose the tokens to the client
        session.accessToken = token.accessToken as string;
        session.jwtToken = token.jwtToken as string;
        session.isUsernameSaved = token.isUsernameSaved as boolean;
        session.isPersonalDetailsSaved =
          token.isPersonalDetailsSaved as boolean;
        session.isWorkDetailsSaved = token.isWorkDetailsSaved as boolean;
        session.isPrivacyPolicyAccepted =
          token.isPrivacyPolicyAccepted as boolean;
      }
      return session;
    },
  },
});
