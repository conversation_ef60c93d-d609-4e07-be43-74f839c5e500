import { apiCall } from '@/lib/api';
import { 
  GlobalSearchParamsI, 
  GlobalSearchCombinedResponseI,
  GlobalSearchResponseI,
  GlobalSearchQuestionItemI 
} from '@/types';

export const globalSearchAPI = async (
  params: GlobalSearchParamsI
): Promise<GlobalSearchCombinedResponseI> => {
  const result = await apiCall<undefined, GlobalSearchCombinedResponseI>(
    'backend/api/v1/forum/global-search',
    'GET',
    {
      isAuth: true,
      query: params,
    }
  );

  return result;
};

export const searchQuestionsAPI = async (
  params: Omit<GlobalSearchParamsI, 'type'>
): Promise<GlobalSearchResponseI<GlobalSearchQuestionItemI>> => {
  const result = await globalSearchAPI({
    ...params,
    type: 'questions'
  });

  return result.questions || { data: [], total: 0 };
};

// For unauthenticated users - this will return truncated content
export const searchQuestionsPublicAPI = async (
  params: Omit<GlobalSearchParamsI, 'type'>
): Promise<GlobalSearchResponseI<GlobalSearchQuestionItemI>> => {
  const result = await apiCall<undefined, GlobalSearchCombinedResponseI>(
    'backend/api/v1/forum/global-search',
    'GET',
    {
      isAuth: false,
      query: {
        ...params,
        type: 'questions'
      },
    }
  );

  return result.questions || { data: [], total: 0 };
};
