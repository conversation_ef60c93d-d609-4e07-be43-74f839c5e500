export type UserI = {
  id: string;
  name: string;
  email: string;
  avatar: string;
  title: string;
  company: string;
  location: string;
  connections: number;
  profileViews: number;
  postImpressions: number;
  isVerified: boolean;
  bio?: string;
  coverImage?: string;
};

export type MediaFileI = {
  id: string;
  url: string;
  name: string;
  type: 'image' | 'pdf' | 'doc' | 'sheet' | 'video' | 'audio' | 'other';
  size?: number;
  mimeType?: string;
};

export type QuestionI = {
  id: string;
  title: string;
  content: string;
  author: UserI;
  createdAt: string;
  updatedAt: string;
  answerCount: number;
  followCount: number;
  isFollowing: boolean;
  tags: string[];
  images?: string[];
  media?: MediaFileI[];
  lastActivity: string;
  topAnswer?: AnswerI;
  upvotes: number;
  downvotes: number;
  isUpvoted: boolean;
  isDownvoted: boolean;
  status?: 'open' | 'solved' | 'closed';
};

export type AnswerI = {
  id: string;
  content: string;
  author: UserI;
  questionId: string;
  createdAt: string;
  updatedAt: string;
  upvotes: number;
  downvotes: number;
  isUpvoted: boolean;
  isDownvoted: boolean;
  images?: string[];
  isAccepted: boolean;
};

export type NotificationI = {
  id: string;
  type: 'answer' | 'follow' | 'upvote' | 'mention';
  message: string;
  createdAt: string;
  isRead: boolean;
  relatedUser?: UserI;
  relatedQuestion?: QuestionI;
  relatedAnswer?: AnswerI;
};

export type FeedItemI = {
  id: string;
  type: 'question';
  question: QuestionI;
  timestamp: string;
};

export type PaginationI = {
  page: number;
  limit: number;
  total: number;
  hasMore: boolean;
};

export type ApiResponseI<T> = {
  data: T;
  pagination?: PaginationI;
  success: boolean;
  message?: string;
};

export type LoadingStateI = 'idle' | 'loading' | 'success' | 'error';

export type ThemeI = 'light' | 'dark';

export type ModalI = {
  isOpen: boolean;
  type?: 'question' | 'answer' | 'profile' | 'settings';
  data?: unknown;
};

// Forum API Types
export type TopicI = {
  id: string;
  name: string;
  dataType: 'master' | 'raw';
};

export type GlobalSearchQuestionItemI = {
  id: string;
  title: string;
  description: string;
  type: string;
  communityId: string;
  communityName: string;
  departmentAlternativeId: string | null;
  departmentName: string | null;
  equipmentCategoryId: string | null;
  equipmentCategoryName: string | null;
  equipmentModelId: string | null;
  equipmentModelName: string | null;
  equipmentManufacturerId: string | null;
  equipmentManufacturerName: string | null;
  answerCount: number;
  upvoteCount: number;
  createdAt: string;
  profileName: string;
  matchedFields: string[];
  topics: TopicI[];
};

export type GlobalSearchCommunityItemI = {
  id: string;
  name: string;
  description: string | null;
  access: 'PUBLIC' | 'PRIVATE' | 'GLOBAL';
  isRestricted: boolean;
  memberCount: number;
  questionCount: number;
  matchedFields: string[];
};

export type GlobalSearchResponseI<T> = {
  data: T[];
  total: number;
};

export type GlobalSearchCombinedResponseI = {
  questions?: GlobalSearchResponseI<GlobalSearchQuestionItemI>;
  communities?: GlobalSearchResponseI<GlobalSearchCommunityItemI>;
};

export type GlobalSearchParamsI = {
  search: string;
  page: number;
  pageSize: number;
  type: 'questions' | 'communities' | 'all';
};

// Extended Question type for API data
export type ApiQuestionI = {
  id: string;
  title: string;
  description: string;
  type: string;
  communityId: string;
  communityName: string;
  departmentName: string | null;
  equipmentCategoryName: string | null;
  equipmentModelName: string | null;
  equipmentManufacturerName: string | null;
  answerCount: number;
  upvoteCount: number;
  createdAt: string;
  profileName: string;
  topics: TopicI[];
  isVerified?: boolean;
  topAnswer?: AnswerI;
  attachments?: MediaFileI[];
  isAuthenticated?: boolean;
  isTruncated?: boolean;
};
