'use client';

import { useState, useCallback } from 'react';
import { ApiQuestionI, GlobalSearchQuestionItemI } from '../types';
import { searchQuestionsAPI, searchQuestionsPublicAPI } from '../networks/forum/globalSearch';

const useQuestions = (isAuthenticated: boolean = true) => {
  const [isLoading, setIsLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Convert API response to our ApiQuestionI format
  const convertApiQuestion = (item: GlobalSearchQuestionItemI): ApiQuestionI => {
    return {
      id: item.id,
      title: item.title,
      description: item.description,
      type: item.type,
      communityId: item.communityId,
      communityName: item.communityName,
      departmentName: item.departmentName,
      equipmentCategoryName: item.equipmentCategoryName,
      equipmentModelName: item.equipmentModelName,
      equipmentManufacturerName: item.equipmentManufacturerName,
      answerCount: item.answerCount,
      upvoteCount: item.upvoteCount,
      createdAt: item.createdAt,
      profileName: item.profileName,
      topics: item.topics,
      isVerified: false, // Will be determined by backend logic
      isAuthenticated,
      isTruncated: !isAuthenticated,
    };
  };

  const loadMoreQuestions = useCallback(
    async (page: number): Promise<ApiQuestionI[]> => {

      setIsLoading(true);
      setError(null);
      try {
        const apiFunction = isAuthenticated ? searchQuestionsAPI : searchQuestionsPublicAPI;
        const response = await apiFunction({
          search: 'question', // Use a generic search term to get questions
          page,
          pageSize: 10,
        });

        const questions = response.data.map(convertApiQuestion);

        if (questions.length === 0 || questions.length < 10) {
          setHasMore(false);
        }

        return questions;
      } catch (err) {
        console.error('Failed to load questions:', err);
        setError('Failed to load questions. Please try again.');
        setHasMore(false);
        return [];
      } finally {
        setIsLoading(false);
      }
    },
    [isAuthenticated, convertApiQuestion]
  );

  const getInitialQuestions = useCallback(async (): Promise<ApiQuestionI[]> => {

    setIsLoading(true);
    setError(null);
    try {

      const questions = await loadMoreQuestions(0);
      return questions;
    } catch (error) {
      console.error('Failed to load initial questions:', error);
      setError('Failed to load questions. Please check your connection.');
      return [];
    } finally {
      setIsLoading(false);
    }
  }, [loadMoreQuestions]);

  return {
    isLoading,
    hasMore,
    error,
    loadMoreQuestions,
    getInitialQuestions,
  };
};

export default useQuestions;
