'use client';

import { useSession } from 'next-auth/react';
import { UserI } from '@/types';

type UseAuthReturnI = {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: UserI | null;
  session: any;
};

const useAuth = (): UseAuthReturnI => {
  const { data: session, status } = useSession();
  
  const isLoading = status === 'loading';
  const isAuthenticated = status === 'authenticated' && !!session?.user;
  
  // Convert NextAuth user to our UserI type
  const user: UserI | null = session?.user ? {
    id: session.user.backendData?.profileId || session.user.id || '',
    name: session.user.name || '',
    email: session.user.email || '',
    avatar: session.user.image || session.user.backendData?.avatar || '',
    title: session.user.backendData?.title || '',
    company: session.user.backendData?.company || '',
    location: session.user.backendData?.location || '',
    connections: session.user.backendData?.connections || 0,
    profileViews: session.user.backendData?.profileViews || 0,
    postImpressions: session.user.backendData?.postImpressions || 0,
    isVerified: session.user.backendData?.isVerified || false,
    bio: session.user.backendData?.bio || '',
  } : null;

  return {
    isAuthenticated,
    isLoading,
    user,
    session,
  };
};

export default useAuth;
