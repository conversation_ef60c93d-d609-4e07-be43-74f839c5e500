import { isFilled, omit } from '@/utilities/data/object';
import { generateUrl } from '@/utilities/networks/api';
import APIResError from '@/errors/networks/APIResError';
import AppError from '@/errors/networks/AppError';

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || '';

export type MethodI = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
export type HeadersI = Record<string, string>;

export type APICallI<PayloadT = unknown> = {
  isAuth?: boolean;
  payload?: PayloadT;
  query?: Record<string, unknown>;
  routeId?: string | number;
  headers?: HeadersI;
};

const getHeaders = async (): Promise<HeadersI> => {
  // For web, we can use cookies or localStorage directly
  const deviceId =
    typeof window !== 'undefined' ? localStorage.getItem('deviceId') || '' : '';

  return {
    'x-api-key': process.env.NEXT_PUBLIC_API_KEY || '',
    Accept: 'application/json',
    'x-device-id': deviceId,
    'x-platform': 'web',
    'x-version-no': process.env.NEXT_PUBLIC_VERSION_NO || '',
  };
};

export const apiCall = async <PayloadT = unknown, ResponseT = unknown>(
  path: string,
  method: MethodI,
  { isAuth = true, payload, query, routeId, headers }: APICallI<PayloadT>
): Promise<ResponseT> => {
  try {
    const baseUrl = BASE_URL;

    const url = generateUrl({
      baseUrl,
      path,
      query,
      routeId,
    });

    let baseHeaders = await getHeaders();

    if (isFilled(headers)) {
      baseHeaders = { ...baseHeaders, ...headers };
    }

    if (['POST', 'PATCH', 'PUT'].includes(method) && isFilled(payload)) {
      baseHeaders['Content-Type'] = 'application/json';
    }

    if (method === 'DELETE') {
      baseHeaders = omit(baseHeaders, ['Content-Type', 'Accept']) as HeadersI;
    }

    if (isAuth) {
      const isServicePath =
        path.startsWith('/communication/') || path.startsWith('/chat/');
      const tokenKey = isServicePath ? 'jwtToken' : 'token';
      const token =
        typeof window !== 'undefined' ? localStorage.getItem(tokenKey) : null;

      if (token) {
        baseHeaders.Authorization = `Bearer ${token}`;
      }
    }

    const options: RequestInit = {
      headers: baseHeaders,
      method,
      credentials: 'include', // For cookies if needed
    };

    if (isFilled(payload)) {
      options.body = JSON.stringify(payload);
    }

    const response = await fetch(url, options);

    const contentType = response.headers.get('content-type');
    const isJson = contentType?.includes('application/json');
    const json = isJson ? await response.json() : null;

    if (response.status >= 200 && response.status < 300) {
      return json as ResponseT;
    } else if (response.status === 401) {
      // Clear auth state on unauthorized
      if (typeof window !== 'undefined') {
        localStorage.removeItem('token');
        localStorage.removeItem('jwtToken');
      }
    }

    throw new APIResError(
      response.status,
      json || { message: 'No response body' }
    );
  } catch (error) {
    if (error instanceof APIResError) {
      throw error;
    } else if (error instanceof TypeError) {
      throw error;
    }
    throw new AppError('Unknown error', error as Error);
  }
};
