'use client';

import AppleIcon from '@assets/svg/apple';
import GoogleIcon from '@assets/svg/google';
import useLoginForm from '../LoginForm/useHook';

const SocialLogin = () => {
  const { handleGoogleLogin, handleAppleLogin } = useLoginForm();

  return (
    <div className="space-y-4">
      <button
        type="button"
        onClick={handleGoogleLogin}
        className="w-full flex justify-center items-center py-3 border border-gray-400 rounded-full text-base font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors"
      >
        <GoogleIcon />
        <span className="ml-2">Continue with Google</span>
      </button>
      <button
        type="button"
        onClick={handleAppleLogin}
        className="w-full flex justify-center items-center py-3 border border-gray-400 rounded-full text-base font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors"
      >
        <AppleIcon />
        <span className="ml-2">Sign in with Apple</span>
      </button>
    </div>
  );
};

export default SocialLogin;
