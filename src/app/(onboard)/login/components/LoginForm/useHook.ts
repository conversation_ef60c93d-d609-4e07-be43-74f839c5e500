'use client';

import { useState, useCallback, useEffect } from 'react';
import { signIn } from 'next-auth/react';
import { CredentialResponse } from '@react-oauth/google';
import { FormDataI, FormErrorsI } from './types';

const useLoginForm = () => {
  const [formData, setFormData] = useState<FormDataI>({
    email: '',
    password: '',
  });

  const [errors, setErrors] = useState<FormErrorsI>({});
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  useEffect(() => {
    if (typeof window !== 'undefined' && (window as any).AppleID) {
      (window as any).AppleID.auth.init({
        clientId: process.env.NEXT_PUBLIC_APPLE_CLIENT_ID!,
        scope: 'name email',
        redirectURI: process.env.NEXT_PUBLIC_APPLE_REDIRECT_URI!,
        state: 'origin:web',
        usePopup: true,
      });
    }
  }, []);

  const handleInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const { name, value } = e.target;
      setFormData(prev => ({ ...prev, [name]: value }));

      if (errors[name as keyof FormErrorsI]) {
        setErrors(prev => ({ ...prev, [name]: undefined }));
      }
    },
    [errors]
  );

  const validateForm = useCallback((): boolean => {
    const newErrors: FormErrorsI = {};

    if (!formData.email.trim()) {
      newErrors.email = 'Please enter your email or phone number';
    }

    if (!formData.password) {
      newErrors.password = 'Please enter your password';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData]);

  const handleSubmit = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault();
      if (!validateForm()) return;

      setIsLoading(true);
      setErrors({});

      try {
        const result = await signIn('credentials', {
          type: 'EMAIL_PASSWORD',
          email: formData.email,
          password: formData.password,
          callbackUrl: '/forums',
          redirect: false,
        });

        if (result?.error) {
          setErrors({
            general: 'Invalid email or password. Please try again.',
          });
        } else if (result?.ok) {
          // Redirect manually on success
          window.location.href = '/forums';
        }
      } catch (error) {
        console.error('Login error:', error);
        setErrors({
          general: 'An error occurred during login. Please try again.',
        });
      }

      setIsLoading(false);
    },
    [formData, validateForm]
  );

  const togglePasswordVisibility = useCallback(() => {
    setShowPassword(prev => !prev);
  }, []);

  const handleGoogleLogin = useCallback(async () => {
    setIsLoading(true);
    setErrors({});
    try {
      // Use NextAuth's Google provider directly
      await signIn('google', {
        callbackUrl: '/forums',
        redirect: true, // Let NextAuth handle the redirect
      });
    } catch (error) {
      console.error('Google sign-in error:', error);
      setErrors({ general: 'Google sign-in failed. Please try again.' });
      setIsLoading(false);
    }
  }, []);

  const onGoogleLoginSuccess = useCallback(
    async (credentialResponse: CredentialResponse) => {
      setIsLoading(true);
      setErrors({});
      if (!credentialResponse.credential) {
        setErrors({ general: 'Google sign-in failed. Please try again.' });
        setIsLoading(false);
        return;
      }
      await signIn('credentials', {
        type: 'GOOGLE',
        externalToken: credentialResponse.credential,
        callbackUrl: '/forums',
      });
      setIsLoading(false);
    },
    []
  );

  const handleAppleLogin = useCallback(async () => {
    setIsLoading(true);
    setErrors({});
    try {
      const response = await (window as any).AppleID.auth.signIn();
      if (response?.authorization?.id_token) {
        await signIn('credentials', {
          type: 'APPLE',
          externalToken: response.authorization.id_token,
          callbackUrl: '/forums',
        });
      } else {
        setErrors({ general: 'Apple sign-in failed. Please try again.' });
      }
    } catch {
      setErrors({ general: 'An error occurred during Apple sign-in.' });
    }
    setIsLoading(false);
  }, []);

  return {
    formData,
    errors,
    isLoading,
    showPassword,
    handleInputChange,
    handleSubmit,
    handleGoogleLogin,
    onGoogleLoginSuccess,
    handleAppleLogin,
    togglePasswordVisibility,
  };
};

export default useLoginForm;
