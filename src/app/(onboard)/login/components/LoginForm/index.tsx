'use client';

import Link from 'next/link';
import { Button, Input } from '@/components';
import useLoginForm from './useHook';
import EyeIcon from '@assets/svg/Eye';
import EyeClosedIcon from '@assets/svg/EyeClosed';

const LoginForm = () => {
  const {
    formData,
    errors,
    isLoading,
    handleInputChange,
    handleSubmit,
    // handleGoogleLogin,
    showPassword,
    togglePasswordVisibility,
  } = useLoginForm();

  return (
    <div className="space-y-4">
      {errors.general && (
        <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
          {errors.general}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-2">
        <div className="mb-5">
          <Input
            id="email"
            name="email"
            type="text"
            autoComplete="username"
            required
            value={formData.email}
            onChange={handleInputChange}
            placeholder="Email or phone"
          />
          {errors.email && (
            <p className="mt-1 text-sm text-red-600">{errors.email}</p>
          )}
        </div>
        <div>
          <div className="relative">
            <Input
              id="password"
              name="password"
              type={showPassword ? 'text' : 'password'}
              autoComplete="current-password"
              required
              value={formData.password}
              onChange={handleInputChange}
              placeholder="Password"
            />

            <button
              type="button"
              className="absolute inset-y-0 right-0 pr-3 flex items-center"
              onClick={togglePasswordVisibility}
            >
              {showPassword ? (
                <EyeClosedIcon
                  className="text-gray-400 hover:text-gray-600"
                  width={20}
                  height={20}
                />
              ) : (
                <EyeIcon
                  className="text-gray-400 hover:text-gray-600"
                  width={20}
                  height={20}
                />
              )}
            </button>
          </div>
          {errors.password && (
            <p className="mt-1 text-sm text-red-600">{errors.password}</p>
          )}
        </div>

        <div className="text-left">
          <Link
            href="/forgot-password"
            className="text-primary text-sm font-semibold hover:underline"
          >
            Forgot password?
          </Link>
        </div>

        <div className="flex items-center mb-4">
          <input
            id="remember-me"
            name="remember-me"
            type="checkbox"
            className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded accent-primary"
          />
          <label
            htmlFor="remember-me"
            className="ml-2 block text-sm text-gray-900"
          >
            Keep me logged in
          </label>
        </div>

        <div className="w-full">
          <Button
            className="w-full rounded-full text-white text-base font-semibold"
            type="submit"
            disabled={isLoading}
          >
            {isLoading ? 'Signing in...' : 'Sign in'}
          </Button>
        </div>
      </form>
    </div>
  );
};
export default LoginForm;
