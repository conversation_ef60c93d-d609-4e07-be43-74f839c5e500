'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { QuestionCardSkeleton } from '../Shimmer';
import { QuestionFeedPropsI } from './type';
import QuestionCard from '../QuestionCard';
import { LoadingStateI, QuestionI, ApiQuestionI } from '@/types';
import useAuth from '@/hooks/useAuth';

const QuestionFeed = ({
  initialQuestions = [],
  onLoadMore,
  hasMore = true,
  isLoading = false,
}: QuestionFeedPropsI) => {
  const { isAuthenticated } = useAuth();
  const [questions, setQuestions] = useState<(QuestionI | ApiQuestionI)[]>(initialQuestions || []);
  const [loadingState, setLoadingState] = useState<LoadingStateI>('idle');
  const [page, setPage] = useState(1);
  const [hasMoreQuestions, setHasMoreQuestions] = useState(hasMore);

  const handleScroll = useCallback(() => {
    if (
      window.innerHeight + document.documentElement.scrollTop >=
        document.documentElement.offsetHeight - 1000 &&
      loadingState !== 'loading' &&
      hasMoreQuestions &&
      onLoadMore
    ) {
      loadMore();
    }
  }, [loadingState, hasMoreQuestions, onLoadMore]);

  const loadMore = async () => {
    if (loadingState === 'loading' || !hasMoreQuestions || !onLoadMore) return;

    setLoadingState('loading');
    try {
      const newQuestions = await onLoadMore(page + 1);
      if (newQuestions.length === 0) {
        setHasMoreQuestions(false);
      } else {
        setQuestions(prev => [...prev, ...newQuestions]);
        setPage(prev => prev + 1);
      }
      setLoadingState('success');
    } catch (error) {
      setLoadingState('error');
      console.error('Failed to load more questions:', error);
    }
  };

  useEffect(() => {
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [handleScroll]);

  useEffect(() => {
    setQuestions(initialQuestions || []);
  }, [initialQuestions]);

  const handleAnswer = (questionId: string) => {
    console.log('Answer question:', questionId);
  };

  const handleFollow = (questionId: string) => {
    setQuestions(prev =>
      prev.map(q => {
        if (q.id === questionId) {
          // Type guard to check if it's a QuestionI (has isFollowing and followCount)
          if ('isFollowing' in q && 'followCount' in q) {
            return {
              ...q,
              isFollowing: !q.isFollowing,
              followCount: q.isFollowing
                ? q.followCount - 1
                : q.followCount + 1,
            };
          }
          // For ApiQuestionI, we don't have these properties, so just return as is
          return q;
        }
        return q;
      })
    );
  };

  const handlePass = (questionId: string) => {
    setQuestions(prev => prev.filter(q => q.id !== questionId));
  };

  if (isLoading && questions.length === 0) {
    return (
      <div className="space-y-4">
        {Array.from({ length: 3 }).map((_, index) => (
          <QuestionCardSkeleton key={index} />
        ))}
      </div>
    );
  }

  if (!isLoading && questions.length === 0) {
    return (
      <div className="bg-white rounded-lg border border-gray-200 p-12 text-center">
        <div className="max-w-md mx-auto">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg
              className="w-8 h-8 text-gray-400"
              fill="currentColor"
              viewBox="0 0 24 24"
            >
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No questions yet
          </h3>
          <p className="text-gray-500 mb-6">
            Be the first to ask a question and start the conversation!
          </p>
          <button className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            Ask a Question
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {questions.map(question => (
        <QuestionCard
          key={question.id}
          question={question}
          onAnswer={handleAnswer}
          onFollow={handleFollow}
          onPass={handlePass}
          isAuthenticated={isAuthenticated}
        />
      ))}

      {loadingState === 'loading' && (
        <div className="space-y-4">
          {Array.from({ length: 2 }).map((_, index) => (
            <QuestionCardSkeleton key={`loading-${index}`} />
          ))}
        </div>
      )}

      {hasMoreQuestions && loadingState !== 'loading' && onLoadMore && (
        <div className="text-center py-6">
          <button
            onClick={loadMore}
            className="inline-flex items-center px-6 py-3 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Load More Questions
          </button>
        </div>
      )}

      {!hasMoreQuestions && questions.length > 0 && (
        <div className="text-center py-6">
          <p className="text-sm text-gray-500">
            You've reached the end of the feed
          </p>
        </div>
      )}

      {loadingState === 'error' && (
        <div className="text-center py-6">
          <p className="text-sm text-red-600 mb-2">
            Failed to load more questions
          </p>
          <button
            onClick={loadMore}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            Try Again
          </button>
        </div>
      )}
    </div>
  );
};

export default QuestionFeed;
