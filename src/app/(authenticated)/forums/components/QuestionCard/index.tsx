'use client';

import Image from 'next/image';
import { QuestionCardPropsI } from './types';
import QuestionButtons from '../QuestionButtons';
import QuestionImageViewer from '../QuestionImageViewer';
import QuestionAttachments from '../QuestionAttachments';
import LoginPrompt from '@/components/LoginPrompt';
import { QuestionI, ApiQuestionI, MediaFileI } from '@/types';

const QuestionCard = ({ question, isAuthenticated = true }: QuestionCardPropsI) => {
  const truncateContent = (content: string, maxLength: number = 200) => {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  };

  // Type guard to check if it's an ApiQuestionI
  const isApiQuestion = (q: QuestionI | ApiQuestionI): q is ApiQuestionI => {
    return 'communityName' in q;
  };

  // Helper function to get media files
  const getMediaFiles = (): MediaFileI[] => {
    if (isApiQuestion(question)) {
      return question.attachments || [];
    }
    return question.media || [];
  };

  // Helper function to get images
  const getImages = (): string[] => {
    if (isApiQuestion(question)) {
      const mediaFiles = getMediaFiles();
      return mediaFiles.filter(file => file.type === 'image').map(file => file.url);
    }
    return question.images || [];
  };

  const mediaFiles = getMediaFiles();
  const imageFiles = mediaFiles.filter(file => file.type === 'image');
  const allImages = [
    ...getImages(),
    ...imageFiles.map(file => file.url),
  ];
  const nonImageFiles = mediaFiles.filter(file => file.type !== 'image');

  // Helper functions for different question types
  const getContent = () => {
    if (isApiQuestion(question)) {
      return question.description;
    }
    return question.content;
  };

  const getTags = () => {
    if (isApiQuestion(question)) {
      return question.topics?.map(topic => topic.name) || [];
    }
    return question.tags || [];
  };

  const getStatus = () => {
    if (isApiQuestion(question)) {
      return question.isVerified ? 'solved' : 'open';
    }
    return question.status || 'open';
  };

  const getAuthorName = () => {
    if (isApiQuestion(question)) {
      return question.profileName;
    }
    return question.author?.name || '';
  };

  const getUpvoteCount = () => {
    if (isApiQuestion(question)) {
      return question.upvoteCount;
    }
    return question.upvotes || 0;
  };

  const getAnswerCount = () => {
    if (isApiQuestion(question)) {
      return question.answerCount;
    }
    return question.answerCount || 0;
  };

  const content = getContent();
  const tags = getTags();
  const status = getStatus();
  const shouldShowTruncated = isApiQuestion(question) && !isAuthenticated;

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6 mb-1.5 hover:shadow-md transition-shadow duration-200 relative">
      {status === 'solved' && (
        <div className="absolute top-4 right-4 z-10">
          <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200">
            ✓ Solved
          </span>
        </div>
      )}

      {tags && tags.length > 0 && (
        <div className="flex flex-wrap gap-2 mb-4 border-gray-100">
          {tags.map((tag, index) => (
            <span
              key={index}
              className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-50 text-blue-700 hover:bg-blue-100 cursor-pointer transition-colors duration-200"
            >
              {tag}
            </span>
          ))}
        </div>
      )}

      <div className="flex justify-between items-start">
        <div className="flex-1">
          <h2 className="text-lg font-medium text-gray-900 leading-tight mb-3 hover:text-blue-600 cursor-pointer">
            {question.title}
          </h2>
        </div>
      </div>

      {content && (
        <div className="mb-4 relative">
          <p className="text-gray-700 leading-relaxed">
            {shouldShowTruncated ? truncateContent(content, 100) : truncateContent(content)}
          </p>
          {shouldShowTruncated && (
            <div className="mt-4">
              <LoginPrompt message="Sign in to read the full question and answers" />
            </div>
          )}
        </div>
      )}

      {!shouldShowTruncated && (
        <>
          <QuestionImageViewer allImages={allImages} />
          <QuestionAttachments nonImageFiles={nonImageFiles} />
        </>
      )}

      {status === 'solved' && question.topAnswer && !shouldShowTruncated && (
        <div className="mb-4 p-4 bg-green-50 border border-green-200 rounded-lg">
          <div className="flex items-center mb-2">
            <span className="text-green-600 font-medium text-sm">
              ✓ Accepted Answer
            </span>
          </div>
          <div className="flex items-start space-x-3">
            <Image
              src={question.topAnswer.author.avatar}
              alt={question.topAnswer.author.name}
              width={32}
              height={32}
              className="rounded-full"
            />
            <div className="flex-1">
              <div className="flex items-center space-x-2 mb-1">
                <span className="font-medium text-sm text-gray-900">
                  {question.topAnswer.author.name}
                </span>
                <span className="text-xs text-gray-500">
                  {question.topAnswer.author.title}
                </span>
              </div>
              <p className="text-sm text-gray-700 leading-relaxed">
                {question.topAnswer.content}
              </p>
              <div className="flex items-center space-x-4 mt-2">
                <span className="text-xs text-gray-500">
                  {question.topAnswer.upvotes} upvotes
                </span>
                <span className="text-xs text-gray-500">
                  {new Date(question.topAnswer.createdAt).toLocaleDateString()}
                </span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Stats and Actions */}
      <div className="flex items-center justify-between pt-4 border-t border-gray-100">
        <div className="flex items-center space-x-6 text-sm text-gray-500">
          <span className="flex items-center space-x-1">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 11l5-5m0 0l5 5m-5-5v12" />
            </svg>
            <span>{getUpvoteCount()}</span>
          </span>
          <span className="flex items-center space-x-1">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
            <span>{getAnswerCount()}</span>
          </span>
          <span className="text-xs">
            by {getAuthorName()}
          </span>
          {isApiQuestion(question) && question.communityName && (
            <span className="text-xs">
              in {question.communityName}
            </span>
          )}
        </div>

        {!shouldShowTruncated && <QuestionButtons />}
      </div>
    </div>
  );
};

export default QuestionCard;
