'use client'
import { DashboardLayout } from '@/components'
import { MOCK_QUESTIONS, MOCK_USER } from '@/constants'
import useQuestions from '@/hooks/useQuestions'
import React from 'react'

const ForumPage = () => {
  const [questions, setQuestions] = React.useState(MOCK_QUESTIONS)
   const {hasMore, loadMoreQuestions, isLoading, getInitialQuestions}  = useQuestions()
   


  return (
    <DashboardLayout
      user={MOCK_USER}
      initialQuestions={questions}
      isLoading={isLoading}
      onLoadMoreQuestions={loadMoreQuestions}
      hasMoreQuestions={hasMore}
    />
  )
}

export default ForumPage